from sqlmodel import Session, func, select

from app.models.domain import AwemeAnalysis


class AwemeAnalysisRepository:
    def __init__(self, session: Session):
        self.session = session

    def save(self, keyword: str, search_result: list, analysis_result: dict):
        aweme_analysis = AwemeAnalysis(
            keyword=keyword,
            search_result=search_result,
            analysis_result=analysis_result,
        )
        self.session.add(aweme_analysis)
        self.session.commit()
        self.session.refresh(aweme_analysis)
        return aweme_analysis

    def get_history_paginated(self, page: int, page_size: int):
        """
        分页获取搜索历史

        Args:
            page: 页码（从1开始）
            page_size: 每页数量

        Returns:
            tuple: (items, total_count)
        """
        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询数据，只选择需要的字段，按创建时间倒序
        statement = (
            select(
                AwemeAnalysis.id,
                AwemeAnalysis.keyword,
                AwemeAnalysis.status,
                AwemeAnalysis.created_at,
            )
            .order_by(AwemeAnalysis.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        items = self.session.exec(statement).all()

        # 查询总数
        count_statement = select(func.count(AwemeAnalysis.id))
        total_count = self.session.exec(count_statement).one()

        return items, total_count
