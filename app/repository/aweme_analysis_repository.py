from sqlmodel import Session

from app.models.domain import AwemeAnalysis


class AwemeAnalysisRepository:
    def __init__(self, session: Session):
        self.session = session

    def save(self, keyword: str, search_result: list, analysis_result: dict):
        aweme_analysis = AwemeAnalysis(
            keyword=keyword,
            search_result=search_result,
            analysis_result=analysis_result,
        )
        self.session.add(aweme_analysis)
        self.session.commit()
        self.session.refresh(aweme_analysis)
        return aweme_analysis
