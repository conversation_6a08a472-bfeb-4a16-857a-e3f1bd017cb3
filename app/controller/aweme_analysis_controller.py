"""
搜索相关的 API 控制器
"""

import logging

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from sqlmodel import Session

from app.db.database import get_session
from app.models.api import (
    SearchHistoryResponse,
    SearchRequest,
    SearchResponse,
)
from app.service import AwemeAnalysisService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analysis", tags=["作品分析"])


# 创建全局 service 实例，避免重复初始化
def get_aweme_analysis_service(
    session: Session = Depends(get_session),
) -> AwemeAnalysisService:
    """获取 AwemeAnalysisService 实例"""
    return AwemeAnalysisService(session)


@router.post("", response_model=SearchResponse)
async def analysis_aweme(
    request: SearchRequest,
    background_tasks: BackgroundTasks,
    service: AwemeAnalysisService = Depends(get_aweme_analysis_service),
):
    """
    分析关键词相关作品接口

    Args:
        request: 搜索请求，包含关键词
        background_tasks: 后台任务
        service: AwemeAnalysisService 实例

    Returns:
        搜索结果和分析结果
    """
    try:
        logger.info(f"开始搜索关键词: {request.keyword}")

        # 执行搜索
        aweme_analysis = await service.search(request.keyword)

        # 后台执行分析任务
        background_tasks.add_task(service.analyze, aweme_analysis)

        # 返回结果
        return SearchResponse(
            id=aweme_analysis.id,
            keyword=aweme_analysis.keyword,
            search_result=aweme_analysis.search_result,
            analysis_result=aweme_analysis.analysis_result,
            status=aweme_analysis.status,
            created_at=aweme_analysis.created_at.isoformat(),
        )

    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/history", response_model=SearchHistoryResponse)
async def get_search_history(
    page: int = 1,
    page_size: int = 10,
    service: AwemeAnalysisService = Depends(get_aweme_analysis_service),
):
    """
    分页获取分析历史

    Args:
        page: 页码（从1开始）
        page_size: 每页数量
        service: AwemeAnalysisService 实例

    Returns:
        分页的搜索历史列表
    """
    try:
        # 使用 service 层获取分页历史数据
        return service.get_history(page, page_size)

    except Exception as e:
        logger.error(f"获取搜索历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取搜索历史失败: {str(e)}")


@router.get("/{search_id}", response_model=SearchResponse)
async def get_search_result(
    search_id: int, service: AwemeAnalysisService = Depends(get_aweme_analysis_service)
):
    """
    根据 ID 获取分析结果

    Args:
        search_id: 分析任务 ID
        service: AwemeAnalysisService 实例

    Returns:
        分析结果详情
    """
    try:
        # 使用 service 层获取搜索结果
        result = service.get_by_id(search_id)

        if not result:
            raise HTTPException(status_code=404, detail="搜索记录不存在")

        return SearchResponse(
            id=result.id,
            keyword=result.keyword,
            search_result=result.search_result,
            analysis_result=result.analysis_result,
            status=result.status,
            created_at=result.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取搜索结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取搜索结果失败: {str(e)}")
