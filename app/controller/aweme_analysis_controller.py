"""
搜索相关的 API 控制器
"""

import logging

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from sqlmodel import Session

from app.db.database import get_session
from app.models.api import (
    SearchHistoryResponse,
    SearchRequest,
    SearchResponse,
)
from app.models.domain import AwemeAnalysis
from app.service import AwemeAnalysisService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analysis", tags=["作品分析"])


@router.post("", response_model=SearchResponse)
async def analysis_aweme(
    request: SearchRequest,
    background_tasks: BackgroundTasks,
):
    """
    抖音关键词搜索接口

    Args:
        request: 搜索请求，包含关键词
        background_tasks: 后台任务

    Returns:
        搜索结果和分析结果
    """
    try:
        logger.info(f"开始搜索关键词: {request.keyword}")

        # 执行搜索
        aweme_analysis_service = AwemeAnalysisService()
        aweme_analysis = await aweme_analysis_service.search(request.keyword)

        # 后台执行分析任务
        background_tasks.add_task(aweme_analysis_service.analyze, aweme_analysis)

        # 返回结果
        return SearchResponse(
            id=aweme_analysis.id,
            keyword=aweme_analysis.keyword,
            search_result=aweme_analysis.search_result,
            analysis_result=aweme_analysis.analysis_result,
            status=aweme_analysis.status,
            created_at=aweme_analysis.created_at.isoformat(),
        )

    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/history", response_model=SearchHistoryResponse)
async def get_search_history(
    page: int = 1, page_size: int = 10, session: Session = Depends(get_session)
):
    """
    分页获取搜索历史

    Args:
        page: 页码（从1开始）
        page_size: 每页数量
        session: 数据库会话

    Returns:
        分页的搜索历史列表
    """
    try:
        # 使用 service 层获取分页历史数据
        aweme_analysis_service = AwemeAnalysisService(session)
        return aweme_analysis_service.get_history(page, page_size)

    except Exception as e:
        logger.error(f"获取搜索历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取搜索历史失败: {str(e)}")


@router.get("/{search_id}", response_model=SearchResponse)
async def get_search_result(search_id: int, session: Session = Depends(get_session)):
    """
    根据 ID 获取搜索结果

    Args:
        search_id: 搜索记录 ID
        session: 数据库会话

    Returns:
        搜索结果详情
    """
    try:
        # 查询搜索结果
        result = session.get(AwemeAnalysis, search_id)

        if not result:
            raise HTTPException(status_code=404, detail="搜索记录不存在")

        return SearchResponse(
            id=result.id,
            keyword=result.keyword,
            search_result=result.search_result,
            analysis_result=result.analysis_result,
            created_at=result.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取搜索结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取搜索结果失败: {str(e)}")
