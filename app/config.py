"""
应用配置
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.crawlers.douyin.core import DouYinCrawler

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


dy_crawler: DouYinCrawler = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("FastAPI 应用启动")
    dy_crawler = DouYinCrawler()
    await dy_crawler.start()
    yield
    await dy_crawler.close()
    logger.info("FastAPI 应用关闭")


def create_app() -> FastAPI:
    """创建 FastAPI 应用实例"""

    # 创建 FastAPI 应用
    app = FastAPI(
        title="作品分析 API",
        description="基于抖音搜索和视频分析的作品分析接口",
        version="1.0.0",
        lifespan=lifespan,
    )

    # 添加 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该设置具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册路由
    from app.controller import aweme_analysis_controller, base_controller

    app.include_router(base_controller.router)
    app.include_router(aweme_analysis_controller.router)

    return app


def get_dy_crawler() -> DouYinCrawler:
    """获取抖音爬虫实例"""
    return dy_crawler
