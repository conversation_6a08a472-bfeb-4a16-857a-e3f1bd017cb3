import logging

from app.config import get_dy_crawler
from app.db.database import get_session
from app.models.domain import AwemeAnalysis
from app.repository import AwemeAnalysisRepository

log = logging.getLogger(__name__)


class AwemeAnalysisService:
    async def search(self, keyword):
        search_result = await get_dy_crawler().search(keyword)
        aweme_analysis = AwemeAnalysisRepository(get_session()).save(
            keyword, search_result, {}
        )
        return aweme_analysis

    async def analyze(self, aweme_analysis: AwemeAnalysis):
        # TODO: 分析视频
        pass
