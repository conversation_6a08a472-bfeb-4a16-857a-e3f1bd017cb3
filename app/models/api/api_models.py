"""
API 请求和响应模型定义
"""

from typing import Dict, List

from pydantic import BaseModel


class SearchRequest(BaseModel):
    """搜索请求模型"""

    keyword: str


class VideoAnalysisRequest(BaseModel):
    """视频分析请求模型"""

    video_path: str
    question: str | None = "请描述这个视频的前5秒内容"


class BatchVideoAnalysisRequest(BaseModel):
    """批量视频分析请求模型"""

    video_paths: List[str]
    question: str | None = "请描述这个视频的前5秒内容"


class SearchResponse(BaseModel):
    """搜索响应模型"""

    id: int
    keyword: str
    search_result: List[Dict] | None
    analysis_result: Dict | None
    status: int
    created_at: str


class VideoAnalysisResponse(BaseModel):
    """视频分析响应模型"""

    success: bool
    result: str | None = None
    error: str | None = None
    video_info: Dict | None = None


class BatchVideoAnalysisResponse(BaseModel):
    """批量视频分析响应模型"""

    results: Dict[str, VideoAnalysisResponse]


class HealthResponse(BaseModel):
    """健康检查响应模型"""

    status: str
    message: str


class ApiInfoResponse(BaseModel):
    """API 信息响应模型"""

    message: str
    version: str


class SearchHistoryItem(BaseModel):
    """搜索历史项模型"""

    id: int
    keyword: str
    status: int
    created_at: str


class SearchHistoryResponse(BaseModel):
    """搜索历史响应模型"""

    items: List[SearchHistoryItem]
    total: int
    page: int
    page_size: int
    total_pages: int
